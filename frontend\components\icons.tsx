import React from 'react';

// Базовый интерфейс для всех иконок
interface IconProps {
  size?: number;
  className?: string;
  color?: string;
}

// Пример иконки - можешь заменить на свои SVG
export const ExampleIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = '', 
  color = 'currentColor' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2L2 7L12 12L22 7L12 2Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CloseIcon: React.FC<IconProps> = ({ 
  size = 22, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} viewBox="0 0 22 22" fill="none" className={className} xmlns="http://www.w3.org/2000/svg">
    <path d="M1 20.799L20.799 1.00002" stroke={color} stroke-width="2" stroke-linecap="round"/>
    <path d="M1 1L20.799 20.799" stroke={color} stroke-width="2" stroke-linecap="round"/>
    </svg>
    
);

export const SendMessaeIcon: React.FC<IconProps> = ({ 
  size = 62, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} className={className} viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="31" cy="31" r="29" stroke={color} stroke-width="4"/>
        <path d="M29 49.5C29 50.6046 29.8955 51.5 31 51.5C32.1046 51.5 33 50.6046 33 49.5L29 49.5ZM32.4142 16.5858C31.6332 15.8047 30.3668 15.8047 29.5858 16.5858L16.8579 29.3137C16.0768 30.0948 16.0768 31.3611 16.8579 32.1421C17.6389 32.9232 18.9053 32.9232 19.6863 32.1421L31 20.8284L42.3137 32.1421C43.0948 32.9232 44.3611 32.9232 45.1421 32.1421C45.9232 31.3611 45.9232 30.0947 45.1421 29.3137L32.4142 16.5858ZM31 49.5L33 49.5L33 18L31 18L29 18L29 49.5L31 49.5Z" fill={color}/>
    </svg>
);
  

export const MicrophoneIcon: React.FC<IconProps> = ({ 
  size = 16, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} fill={color} viewBox="0 0 16 16" className={className}>
        <path
            fillRule="evenodd"
            d="M2.975 8.002a.5.5 0 0 1 .547.449 4.5 4.5 0 0 0 8.956 0 .5.5 0 1 1 .995.098A5.502 5.502 0 0 1 8.5 13.478V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .448-.547z"
            clipRule="evenodd"
        />
        <path d="M5 3a3 3 0 1 1 6 0v5a3 3 0 0 1-6 0z" />
    </svg>
);

export const MicrophoneDisabledIcon: React.FC<IconProps> = ({ 
  size = 16, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} fill={color} viewBox="0 0 16 16" className={className}>
        <path d="M12.227 11.52a5.477 5.477 0 0 0 1.246-******** 0 0 0-.995-.1 4.478 4.478 0 0 1-.962 2.359l-1.07-1.07C10.794 9.247 11 8.647 11 8V3a3 3 0 0 0-6 0v1.293L1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708zM8 12.5c.683 0 1.33-.152 1.911-.425l.743.743c-.649.359-1.378.59-2.154.66V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .995-.098A4.5 4.5 0 0 0 8 12.5z" />
        <path d="M8.743 10.907 5 7.164V8a3 3 0 0 0 3.743 2.907z" />
    </svg>
);

export const AgentUnmutedIcon: React.FC<IconProps> = ({ 
  size = 40, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M17.6208 20.7924C15.7974 20.7924 14.0149 20.2517 12.4988 19.2386C10.9826 18.2256 9.80096 16.7857 9.10316 15.1011C8.40536 13.4164 8.22279 11.5627 8.57852 9.77433C8.93425 7.98593 9.81232 6.34319 11.1017 5.05383C12.391 3.76447 14.0338 2.8864 15.8222 2.53067C17.6106 2.17493 19.4643 2.35751 21.1489 3.05531C22.8336 3.7531 24.2734 4.93478 25.2865 6.45091C26.2995 7.96704 26.8402 9.74952 26.8402 11.5729C26.8375 14.0173 25.8653 16.3607 24.1369 18.0891C22.4085 19.8174 20.0651 20.7897 17.6208 20.7924ZM17.6208 5.21181C16.3624 5.21181 15.1323 5.58498 14.086 6.28412C13.0397 6.98327 12.2242 7.97699 11.7427 9.13961C11.2612 10.3022 11.1353 11.5815 11.3808 12.8157C11.6264 14.0499 12.2325 15.1836 13.1224 16.0733C14.0123 16.963 15.146 17.5689 16.3803 17.8143C17.6145 18.0596 18.8938 17.9335 20.0563 17.4517C21.2189 16.97 22.2124 16.1544 22.9114 15.108C23.6104 14.0615 23.9833 12.8313 23.9831 11.5729C23.981 9.88632 23.3099 8.2694 22.1172 7.07688C20.9245 5.88436 19.3074 5.21362 17.6208 5.21181Z" fill={color} />
    <path d="M33.5944 39.7143C33.2581 39.7139 32.9327 39.5948 32.6756 39.378C32.4184 39.1612 32.246 38.8606 32.1887 38.5292C31.6878 35.0414 29.9316 31.8561 27.2495 29.5709C24.5675 27.2857 21.1438 26.0574 17.6207 26.1166C14.0976 26.0574 10.674 27.2857 7.99189 29.5709C5.30983 31.8561 3.55364 35.0414 3.05271 38.5292C2.98815 38.9026 2.77789 39.2351 2.46819 39.4535C2.15849 39.6719 1.77471 39.7583 1.40129 39.6938C1.02786 39.6292 0.695378 39.4189 0.476978 39.1092C0.258579 38.7995 0.172153 38.4158 0.236715 38.0423C0.838406 33.8845 2.93614 30.089 6.13655 27.3674C9.33697 24.6459 13.4202 23.1853 17.6207 23.2595C21.8212 23.1853 25.9045 24.6459 29.1049 27.3674C32.3053 30.089 34.403 33.8845 35.0047 38.0423C35.0365 38.2272 35.0317 38.4166 34.9903 38.5996C34.9489 38.7826 34.8719 38.9557 34.7637 39.1089C34.6554 39.2622 34.5181 39.3926 34.3594 39.4928C34.2007 39.5929 34.0239 39.6608 33.839 39.6926C33.7583 39.7071 33.6764 39.7144 33.5944 39.7143Z" fill={color}/>
    <path d="M37.2413 12.0689C37.2413 7.75539 35.8411 5.11616 34.9332 3.82607L34.7588 3.58631L34.6793 3.46912C34.3186 2.87041 34.4631 2.08392 35.0342 1.65541C35.6055 1.22694 36.401 1.30849 36.8749 1.82243L36.9651 1.93087L37.1941 2.24606C38.3698 3.92001 39.9999 7.09863 39.9999 12.0689C39.9999 14.7073 39.5397 16.6946 38.9081 18.2347C38.2781 19.7712 37.4979 20.8076 36.9712 21.5099L36.9651 21.5173C36.508 22.1266 35.6436 22.2498 35.0342 21.7928C34.425 21.3357 34.3018 20.4712 34.7588 19.8619L34.7642 19.8545C35.2725 19.1767 35.8686 18.3775 36.3563 17.1881C36.8425 16.0024 37.2413 14.3731 37.2413 12.0689Z" fill={color}/>
    <path d="M31.7241 11.9363C31.7241 9.51531 31.1288 7.99691 30.7118 7.21513L30.5461 6.92485L30.4788 6.80026C30.1774 6.16965 30.3965 5.40057 31.0061 5.02898C31.616 4.65734 32.4003 4.81523 32.8225 5.37246L32.902 5.48897L33.0239 5.69641C33.6505 6.79456 34.4827 8.81685 34.4827 11.9363C34.4827 13.5912 34.2482 14.8525 33.9176 15.8446C33.5864 16.8388 33.1733 17.5139 32.9047 17.9546L32.902 17.9593C32.5056 18.6098 31.6566 18.8157 31.0061 18.4193C30.3557 18.0229 30.1498 17.1739 30.5461 16.5235L30.5488 16.5187C30.7978 16.1102 31.0728 15.6556 31.3005 14.9724C31.5288 14.287 31.7241 13.3229 31.7241 11.9363Z" fill={color}/>
    </svg>
    
);

export const AgentMutedIcon: React.FC<IconProps> = ({ 
  size = 40, 
  className = '', 
  color = '#FFE79E' 
}) => (
    <svg width={size} height={size} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M17.6208 20.7924C15.7974 20.7924 14.0149 20.2517 12.4988 19.2386C10.9826 18.2256 9.80096 16.7857 9.10316 15.1011C8.40536 13.4164 8.22279 11.5627 8.57852 9.77433C8.93425 7.98593 9.81232 6.34319 11.1017 5.05383C12.391 3.76447 14.0338 2.8864 15.8222 2.53067C17.6106 2.17493 19.4643 2.35751 21.1489 3.05531C22.8336 3.7531 24.2734 4.93478 25.2865 6.45091C26.2995 7.96704 26.8402 9.74952 26.8402 11.5729C26.8375 14.0173 25.8653 16.3607 24.1369 18.0891C22.4085 19.8174 20.0651 20.7897 17.6208 20.7924ZM17.6208 5.21181C16.3624 5.21181 15.1323 5.58498 14.086 6.28412C13.0397 6.98327 12.2242 7.97699 11.7427 9.13961C11.2612 10.3022 11.1353 11.5815 11.3808 12.8157C11.6264 14.0499 12.2325 15.1836 13.1224 16.0733C14.0123 16.963 15.146 17.5689 16.3803 17.8143C17.6145 18.0596 18.8938 17.9335 20.0563 17.4517C21.2189 16.97 22.2124 16.1544 22.9114 15.108C23.6104 14.0615 23.9833 12.8313 23.9831 11.5729C23.981 9.88632 23.3099 8.2694 22.1172 7.07688C20.9245 5.88436 19.3074 5.21362 17.6208 5.21181Z" fill={color} fill-opacity="0.4"/>
    <path d="M33.5944 39.7143C33.2581 39.7139 32.9327 39.5948 32.6756 39.378C32.4184 39.1612 32.246 38.8606 32.1887 38.5292C31.6878 35.0414 29.9316 31.8561 27.2495 29.5709C24.5675 27.2857 21.1438 26.0574 17.6207 26.1166C14.0976 26.0574 10.674 27.2857 7.99189 29.5709C5.30983 31.8561 3.55364 35.0414 3.05271 38.5292C2.98815 38.9026 2.77789 39.2351 2.46819 39.4535C2.15849 39.6719 1.77471 39.7583 1.40129 39.6938C1.02786 39.6292 0.695378 39.4189 0.476978 39.1092C0.258579 38.7995 0.172153 38.4158 0.236715 38.0423C0.838406 33.8845 2.93614 30.089 6.13655 27.3674C9.33697 24.6459 13.4202 23.1853 17.6207 23.2595C21.8212 23.1853 25.9045 24.6459 29.1049 27.3674C32.3053 30.089 34.403 33.8845 35.0047 38.0423C35.0365 38.2272 35.0317 38.4166 34.9903 38.5996C34.9489 38.7826 34.8719 38.9557 34.7637 39.1089C34.6554 39.2622 34.5181 39.3926 34.3594 39.4928C34.2007 39.5929 34.0239 39.6608 33.839 39.6926C33.7583 39.7071 33.6764 39.7144 33.5944 39.7143Z" fill={color} fill-opacity="0.4"/>
    <path d="M31.4138 15.7234L37.6208 9.5164" stroke={color} stroke-width="4" stroke-linecap="round"/>
    <path d="M31.4138 9.51648L37.6208 15.7235" stroke={color} stroke-width="4" stroke-linecap="round"/>
    </svg>
    
);

export const StartStopIcon: React.FC<IconProps> = ({ 
  size = 66, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 62 66"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M3 20V53.9013C3 60.8289 10.4697 65.208 16.5479 61.8435L41.6207 47.9656M3 21V12.0987C3 5.17104 10.4697 0.792059 16.5479 4.15644L54.3095 25.0577C60.5635 28.5195 60.5635 37.4805 54.3095 40.9422L35.4287 51.393" stroke={color} stroke-width="6" stroke-linecap="round"/>
  </svg>
);

export const NextSlideIcon: React.FC<IconProps> = ({ 
  size = 58, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 56 58"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M3 27C1.89543 27 1 27.8954 1 29C1 30.1046 1.89543 31 3 31V27ZM54.4142 30.4142C55.1953 29.6332 55.1953 28.3668 54.4142 27.5858L41.6863 14.8579C40.9052 14.0768 39.6389 14.0768 38.8579 14.8579C38.0768 15.6389 38.0768 16.9052 38.8579 17.6863L50.1716 29L38.8579 40.3137C38.0768 41.0948 38.0768 42.3611 38.8579 43.1421C39.6389 43.9232 40.9052 43.9232 41.6863 43.1421L54.4142 30.4142ZM3 29V31H53V29V27H3V29Z" fill={color}/>
  </svg>
);

export const NextSlideDisabledIcon: React.FC<IconProps> = ({ 
  size = 58, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 56 58"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M18 27C16.8954 27 16 27.8954 16 29C16 30.1046 16.8954 31 18 31V27ZM39.4142 30.4142C40.1953 29.6332 40.1953 28.3668 39.4142 27.5858L26.6863 14.8579C25.9052 14.0768 24.6389 14.0768 23.8579 14.8579C23.0768 15.6389 23.0768 16.9052 23.8579 17.6863L35.1716 29L23.8579 40.3137C23.0768 41.0948 23.0768 42.3611 23.8579 43.1421C24.6389 43.9232 25.9052 43.9232 26.6863 43.1421L39.4142 30.4142ZM18 29V31H38V29V27H18V29Z" fill={color} fill-opacity="0.4"/>
  </svg>
);

export const PreviousSlideDisabledIcon: React.FC<IconProps> = ({ 
  size = 58, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 56 58"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M38 31C39.1046 31 40 30.1046 40 29C40 27.8954 39.1046 27 38 27V31ZM16.5858 27.5858C15.8047 28.3668 15.8047 29.6332 16.5858 30.4142L29.3137 43.1421C30.0948 43.9232 31.3611 43.9232 32.1421 43.1421C32.9232 42.3611 32.9232 41.0948 32.1421 40.3137L20.8284 29L32.1421 17.6863C32.9232 16.9052 32.9232 15.6389 32.1421 14.8579C31.3611 14.0768 30.0948 14.0768 29.3137 14.8579L16.5858 27.5858ZM38 29V27L18 27V29V31L38 31V29Z" fill={color} fill-opacity="0.4"/>
  </svg>
);

export const PreviousSlideIcon: React.FC<IconProps> = ({ 
  size = 58, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 56 58"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M53 31C54.1046 31 55 30.1046 55 29C55 27.8954 54.1046 27 53 27V31ZM1.58579 27.5858C0.804737 28.3668 0.804737 29.6332 1.58579 30.4142L14.3137 43.1421C15.0948 43.9232 16.3611 43.9232 17.1421 43.1421C17.9232 42.3611 17.9232 41.0948 17.1421 40.3137L5.82843 29L17.1421 17.6863C17.9232 16.9052 17.9232 15.6389 17.1421 14.8579C16.3611 14.0768 15.0948 14.0768 14.3137 14.8579L1.58579 27.5858ZM53 29V27L3 27V29V31L53 31V29Z" fill={color} />
  </svg>
);

// Добавляй сюда новые иконки по аналогии
export const ChevronLeftIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 18L9 12L15 6"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronRightIcon: React.FC<IconProps> = ({ 
  size = 24, 
  className = '', 
  color = '#FFE79E' 
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 18L15 12L9 6"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Экспорт всех иконок для удобного импорта
export const Icons = {
  Example: ExampleIcon,
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon,
};
