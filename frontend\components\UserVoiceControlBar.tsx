import { Track } from 'livekit-client';
import * as React from 'react';
import {
  BarVisualizer,
  DisconnectButton,
  StartMediaButton,
  useLocalParticipant,
  useLocalParticipantPermissions,
} from '@livekit/components-react';
import { TrackToggle } from './controls/TrackToggle';
import { MediaDeviceMenu } from './prefabs/MediaDeviceMenu';
import { wasClickOutside, type TrackReferenceOrPlaceholder } from '@livekit/components-core';
import { ChevronLeftIcon, ChevronRightIcon } from './icons';

/** @beta */
export type UserVoiceControlBarControls = {
  microphone?: boolean;
  leave?: boolean;
};

/** @beta */
export interface UserVoiceControlBarProps extends React.HTMLAttributes<HTMLDivElement> {
  onDeviceError?: (error: { source: Track.Source; error: Error }) => void;
  controls?: UserVoiceControlBarControls;
  /**
   * If `true`, the user's device choices will be persisted.
   * This will enables the user to have the same device choices when they rejoin the room.
   * @defaultValue true
   */
  saveUserChoices?: boolean;
}

/**
 * @example
 * ```tsx
 * <LiveKitRoom ... >
 *   <UserVoiceControlBar />
 * </LiveKitRoom>
 * ```
 * @beta
 */
export function UserVoiceControlBar({
  controls,
  saveUserChoices = true,
  onDeviceError,
  ...props
}: UserVoiceControlBarProps) {
  const visibleControls = { leave: true, microphone: true, ...controls };

  const localPermissions = useLocalParticipantPermissions();
  const { microphoneTrack, localParticipant } = useLocalParticipant();
  
  const [isMicroEnabled, setIsMicroEnabled] = React.useState(false);
  const [isMicrophoneSettingsVisible, setIsMicrophoneSettingsVisible] = React.useState(false);
  
  const button = React.useRef<HTMLDivElement>(null);

  const micTrackRef: TrackReferenceOrPlaceholder = React.useMemo(() => {
    return {
      participant: localParticipant,
      source: Track.Source.Microphone,
      publication: microphoneTrack,
    };
  }, [localParticipant, microphoneTrack]);

  if (!localPermissions) {
    visibleControls.microphone = false;
  } else {
    visibleControls.microphone ??= localPermissions.canPublish;
  }

  const htmlProps = React.useMemo(() => {
    return { className: 'lk-agent-control-bar', ...props };
  }, [props]);

  // Упрощенная версия без usePersistentUserChoices для совместимости
  const microphoneOnChange = React.useCallback(
    (enabled: boolean, isUserInitiated: boolean) => {
      // Можно добавить логику сохранения настроек пользователя
      console.log('Microphone changed:', enabled, 'User initiated:', isUserInitiated);
      setIsMicrophoneSettingsVisible(enabled);
      setIsMicroEnabled(enabled);
    },
    [],
  );

  const handleClickOutside = React.useCallback(
      (event: MouseEvent) => {
        if (!button.current) {
          return;
        }

        if (event.target === button.current) {
          return;
        }

        if (isMicrophoneSettingsVisible && wasClickOutside(button.current, event)) {
          setIsMicrophoneSettingsVisible(false);
        }
      },
      [isMicrophoneSettingsVisible, button],
    );
  
    React.useEffect(() => {
      document.addEventListener<'click'>('click', handleClickOutside);
      return () => {
        document.removeEventListener<'click'>('click', handleClickOutside);
      };
    }, [handleClickOutside]);

  return (
    <div {...htmlProps}>
      {visibleControls.microphone && (
        <>
        <div 
          className="lk-button-group opacity-70 lg:opacity-20 lg:hover:opacity-70 transition-all" 
          ref={button}
        >
          <TrackToggle
            source={Track.Source.Microphone}
            showIcon={true}
            onChange={microphoneOnChange}
            onDeviceError={(error) => onDeviceError?.({ source: Track.Source.Microphone, error })}
            className={`max-w-[180px] h-[10px] !p-3 lg:h-auto  ${!isMicroEnabled ? '!rounded-tr-[var(--lk-border-radius)] !rounded-br-[var(--lk-border-radius)]' : ''}`}
            
          >
            {(isMicrophoneSettingsVisible && isMicroEnabled) && (
              <BarVisualizer trackRef={micTrackRef} barCount={5} options={{ minHeight: 1 }} className='max-w-[100px] !h-[15px] !bg-transparent' />
            )}
            
          </TrackToggle>
          
          {(isMicrophoneSettingsVisible && isMicroEnabled) && (
            <div className="lk-button-group-menu flex flex-col h-[24px] lg:h-[40px] lg:w-[40px] ">
              
            <MediaDeviceMenu
              kind="audioinput"
              onActiveDeviceChange={(_kind, deviceId) => {
                console.log('Audio device changed:', deviceId);
              }}
            />
          </div>
          )} 

          {isMicroEnabled && (
            (!isMicrophoneSettingsVisible ? (
            <button 
              className="lk-button !rounded-tl-[0] !rounded-bl-[0] h-[24px] w-[24px] lg:h-[40px] lg:w-[40px]" 
              onClick={() => setIsMicrophoneSettingsVisible(true)}
            >
              <ChevronRightIcon className="h-[12px] w-[12px] lg:h-[24px] lg:w-[24px]"/>
            </button>
            ) : (
              <button 
              className="lk-button !rounded-tl-[0] !rounded-bl-[0] h-[24px] w-[24px] lg:h-[40px] lg:w-[40px]" 
              onClick={() => setIsMicrophoneSettingsVisible(false)}
            >
              <ChevronLeftIcon className="h-[12px] w-[12px] lg:h-[24px] lg:w-[24px]"/>
            </button>
              )
            )
          )}
          
        </div>
        
          </>
      )}

      {visibleControls.leave && <DisconnectButton>{'Отключиться'}</DisconnectButton>}
      <StartMediaButton />
    </div>
  );
}

export default UserVoiceControlBar;
