import { motion } from "framer-motion";

export default function DisconnectedPanel({
  isConnecting,
  onConnectButtonClicked,
  connectionError,
}: {
  isConnecting: boolean;
  onConnectButtonClicked: () => void;
  connectionError?: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
      className="flex h-full w-full flex-col items-center justify-center"
    >
      <motion.button
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="uppercase rounded-3xl bg-white/10 px-10 py-3 font-semibold text-white no-underline backdrop-blur-sm transition hover:bg-white/20"
        onClick={onConnectButtonClicked}
        disabled={isConnecting}
      >
        {isConnecting ? "Загрузка..." : "Начать презентацию"}
      </motion.button>
      {isConnecting && (
        <p className="text-center mt-2 text-sm text-gray-400">
          Пожалуйста, подождите, агент инициализируется...
        </p>
      )}
      {connectionError && (
        <p className="text-center mt-2 text-sm text-red-500">
          Ошибка: {connectionError}
        </p>
      )}
    </motion.div>
  );
}
