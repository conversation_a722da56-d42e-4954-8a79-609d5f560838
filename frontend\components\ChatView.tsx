import { useChat, useLocalParticipant, useTrackTranscription, useVoiceAssistant } from "@livekit/components-react";
import { useCallback, useEffect, useRef, useState } from "react";
import type { ChatMessage as ComponentsChatMessage } from "@livekit/components-react";
import { LocalParticipant, type Participant, Track, type TranscriptionSegment } from "livekit-client";
import { SendMessaeIcon } from "./icons";

type ChatMessageType = {
  name: string;
  message: string;
  isSelf: boolean;
  timestamp: number;
};

type ChatBlockProps = {
  messages: ChatMessageType[];
  onSend?: (message: string) => Promise<ComponentsChatMessage>;
};

type ChatMessageInput = {
  placeholder: string;
  onSend?: (message: string) => void;
};

type ChatMessageProps = {
  message: string;
  name: string;
  isSelf: boolean;
  hideName?: boolean;
};

export default function ChatView() {
  const {agentTranscriptions, audioTrack} = useVoiceAssistant();
  const localParticipant = useLocalParticipant();
  const localMessages = useTrackTranscription({
    publication: localParticipant.microphoneTrack,
    source: Track.Source.Microphone,
    participant: localParticipant.localParticipant,
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [transcripts, setTranscripts] = useState<Map<string, ChatMessageType>>(
    new Map()
  );
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const { chatMessages, send: sendChat } = useChat();

  useEffect(() => {
    if (audioTrack) {
      for (const s of agentTranscriptions) {
        transcripts.set(
          s.id,
          segmentToChatMessage(
            s,
            transcripts.get(s.id),
            audioTrack.participant
          )
        );
      }
    }
    
    for (const s of localMessages.segments) {
      transcripts.set(
        s.id,
        segmentToChatMessage(
          s,
          transcripts.get(s.id),
          localParticipant.localParticipant
        )
      );
    }

    const allMessages = Array.from(transcripts.values());
    for (const msg of chatMessages) {
      const isAgent = audioTrack
        ? msg.from?.identity === audioTrack.participant?.identity
        : msg.from?.identity !== localParticipant.localParticipant.identity;
      const isSelf =
        msg.from?.identity === localParticipant.localParticipant.identity;
      let name = msg.from?.name;
      if (!name) {
        if (isAgent) {
          name = "Agent";
        } else if (isSelf) {
          name = "You";
        } else {
          name = "Unknown";
        }
      }
      allMessages.push({
        name,
        message: msg.message,
        timestamp: msg.timestamp,
        isSelf: isSelf,
      });
    }
    allMessages.sort((a, b) => a.timestamp - b.timestamp);
    setMessages(allMessages);
  }, [
    transcripts,
    chatMessages,
    localParticipant.localParticipant,
    audioTrack?.participant,
    agentTranscriptions,
    localMessages.segments,
    audioTrack,
  ]);
  
  return (
    <div className={"flex flex-col h-full p-4"}>
      <div className={"flex flex-col items-center grow w-full max-h-full"}>
        <ChatBlock messages={messages} onSend={sendChat} />
      </div>
    </div>
  );
}

const ChatBlock = ({ messages, onSend }: ChatBlockProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // biome-ignore lint/correctness/useExhaustiveDependencies: messages are requred for scrolling during streaming
    useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [containerRef, messages]);

  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <div
        ref={containerRef}
        className="flex flex-col-reverse overflow-y-auto h-[calc(100%-30px)] lg:h-[calc(100%-200px)]"
      >
        <div className="flex flex-col h-fit max-h-full justify-end">
          {messages.map((message, index, allMsg) => {
            const hideName =
              index >= 1 && allMsg[index - 1].name === message.name;

            const messageKey = `${message.timestamp}-${message.name}-${index}`;
            return (
              <ChatMessage
                key={messageKey}
                hideName={hideName}
                name={message.name}
                message={message.message}
                isSelf={message.isSelf}
              />
            );
          })}
        </div>
      </div>
      <ChatMessageInput
        placeholder="Type a message"
        onSend={onSend}
      />
    </div>
  );
};

const ChatMessageInput = ({
  placeholder,
  onSend,
}: ChatMessageInput) => {
  const [message, setMessage] = useState("");
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [inputHasFocus, setInputHasFocus] = useState(false);

  const handleSend = useCallback(() => {
    if (!onSend) {
      return;
    }
    if (message === "") {
      return;
    }

    onSend(message);
    setMessage("");
  }, [onSend, message]);

  useEffect(() => {
    if (message === "") {
      setIsTyping(false);
      return;
    }
    
    setIsTyping(true);
    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 500);

    return () => clearTimeout(timeout);
  }, [message]);

  return (
    <div
      className="flex flex-col gap-2 h-[100px] lg:h-[200px]"
    >
      <div className="flex flex-row lg:flex-col pb-10 lg:pb-0 pt-3 gap-2 items-center relative">
        <div
          className={`w-2 h-4 bg-${inputHasFocus ? "primary" : "secondary"} absolute left-2 ${
            !isTyping && inputHasFocus ? "cursor-animation" : ""
          }`}
        />
        <textarea
          ref={inputRef}
          className={"w-full text-xs bg-background opacity-20 text-secondary p-2 pr-6 rounded-sm lg:h-[100px] focus:opacity-70 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary resize-none"}
          style={{
            paddingLeft: message.length > 0 ? "12px" : "24px",
            caretShape: "block",
          }}
          placeholder={placeholder}
          value={message}
          onChange={(e) => {
            setMessage(e.target.value);
          }}
          onFocus={() => {
            setInputHasFocus(true);
          }}
          onBlur={() => {
            setInputHasFocus(false);
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSend();
            }
          }}
        />
        <button
          type="button"
          disabled={message.length === 0 || !onSend}
          onClick={handleSend}
          className={`${message.length === 0 || !onSend ? "opacity-40" : "opacity-100"} lg:pt-[20px]`}
        >
          <SendMessaeIcon className="h-[24px] w-[24px] lg:h-auto lg:w-auto" />
        </button>
      </div>
    </div>
  );
};

const ChatMessage = ({
  name,
  message,
  isSelf,
  hideName,
}: ChatMessageProps) => {
  return (
    <div className={`flex flex-col gap-1 ${hideName ? "pt-0" : "pt-6"}`}>
      {!hideName && (
        <div
          className={`text-secondary uppercase text-xs`}
        >
          {name}
        </div>
      )}
      <div
        className={`pr-4 text-primary text-sm whitespace-pre-line`}
      >
        {message}
      </div>
    </div>
  );
};

function segmentToChatMessage(
  s: TranscriptionSegment,
  existingMessage: ChatMessageType | undefined,
  participant: Participant
): ChatMessageType {
  const msg: ChatMessageType = {
    message: s.final ? s.text : `${s.text} ...`,
    name: participant instanceof LocalParticipant ? "You" : "Agent",
    isSelf: participant instanceof LocalParticipant,
    timestamp: existingMessage?.timestamp ?? Date.now(),
  };
  return msg;
}