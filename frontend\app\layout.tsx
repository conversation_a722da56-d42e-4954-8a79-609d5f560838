import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import { Analytics } from '@vercel/analytics/react';
import "./globals.css";
import "@livekit/components-styles";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Presentation",
  description: "AI-powered presentation assistant",
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black-translucent',
    title: 'Presentation',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ru" className="h-full">
      <body className={`${inter.className} h-full`}>
        {children}
        <Analytics />
      </body>
    </html>
  );
}