export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
};

export type AgentState = 'idle' | 'speaking' | 'listening';

export interface Viewport {
  x: number;
  y: number;
  zoom: number;
}

export interface Slide {
  index: number;
  title: string;
  url: string; // URL to the image/asset
  videoUrl?: string; // URL to the video file
  description: string; // Text for TTS
  short_description?: string;
  layout: string; // e.g., 'split_text_image', 'full_image'
  type?: 'video' | 'image';
}

export interface Presentation {
  title: string;
  slides: Slide[];
}
