export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
};

export type AgentState = 'idle' | 'speaking' | 'listening';

export interface Viewport {
  x: number;
  y: number;
  zoom: number;
}

export interface Slide {
  index: number;
  title: string;
  url: string; // URL to the image/asset
  videoUrl?: string; // URL to the video file
  presentation_text: string; // Текст для TTS
  technical_description?: string; // Технические детали
  area?: string; // Площадь помещения
  layout: string; // e.g., 'split_text_image', 'full_image'
  type?: 'video' | 'image';
}

export interface Presentation {
  name: string;
  description: string;
  cover: string;
  slides: Slide[];
}

export interface PresentationState {
  slides: Slide[];
  viewport: Viewport;
  currentSlideIndex: number;
  agentState: AgentState;
  audioEnabled: boolean;
  language: string;
  name: string;
  description: string;
  cover: string;
  serverUrl?: string;
  token?: string;
  isConnecting: boolean;
  connectionError: string | null;
  isPlaying: boolean; // Флаг автоматического проигрывания
  
  setSlides: (slides: Slide[]) => void;
  setViewport: (viewport: Partial<Viewport>) => void;
  setCurrentSlideIndex: (index: number) => void;
  setAgentState: (state: AgentState) => void;
  setAudioEnabled: (enabled: boolean) => void;
  setLanguage: (language: string) => void;
  goToNextSlide: () => void;
  goToPrevSlide: () => void;
  setPresentation: (payload: { name: string; description: string; cover: string; slides: Slide[] }) => void;
  connect: (language: string) => Promise<void>;
  disconnect: () => void;
  startPresentation: () => void; // Запуск автоматического проигрывания
}
