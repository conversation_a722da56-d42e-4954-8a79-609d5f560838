import React from 'react';
import { LayoutVariant, LayoutProps } from '@/components/layouts/types';

// Import layout components
import SplitTextImageLayout from '@/components/layouts/SplitTextImageLayout';
import FullImageLayout from '@/components/layouts/FullImageLayout';
import FullVideoLayout from '@/components/layouts/FullVideoLayout';

export const layoutRegistry: Record<LayoutVariant, React.FC<LayoutProps>> = {
  split_text_image: SplitTextImageLayout,
  full_image: FullImageLayout,
  full_video: FullVideoLayout,
};

