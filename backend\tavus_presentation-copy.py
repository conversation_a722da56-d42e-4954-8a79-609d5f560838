import logging
import json
import uuid
import os
import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, List, Dict, Any, TypedDict
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, llm, WorkerPermissions, RoomOutputOptions

load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))
from livekit.agents.llm import function_tool
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.plugins import openai, deepgram, tavus, elevenlabs, rime, turn_detector
from livekit.plugins.turn_detector.multilingual import MultilingualModel
import asyncio
import webrtcvad

# Добавляем цветной лог
class ColorFormatter(logging.Formatter):
    COLORS = {
        'DEBUG': '\033[94m',    # Синий
        'INFO': '\033[92m',     # Зелёный
        'WARNING': '\033[93m',  # Жёлтый
        'ERROR': '\033[91m',    # Красный
        'CRITICAL': '\033[95m'  # Фиолетовый
    }
    RESET = '\033[0m'

    def format(self, record):
        color = self.COLORS.get(record.levelname, '')
        message = super().format(record)
        return f"{color}{message}{self.RESET}"

logger = logging.getLogger("avatar")
logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setFormatter(ColorFormatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(ch)

class WebRTCVAD:
    def __init__(self, aggressiveness=3):
        import webrtcvad
        self.vad = webrtcvad.Vad(aggressiveness)
        self.sample_rate = 16000
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000) * 2

    def is_speech(self, audio_bytes: bytes) -> bool:
        return self.vad.is_speech(audio_bytes, self.sample_rate)

    def __call__(self, chunk: bytes) -> float:
        is_speech = self.is_speech(chunk)
        logger.debug(f"[VAD] {'Speech' if is_speech else 'Silence'} detected")
        return 1.0 if is_speech else 0.0

    def stream(self):
        class WebRTCVADStream:
            def __init__(self, vad):
                self.vad = vad
                self.queue = asyncio.Queue()

            def push_frame(self, frame: bytes):
                is_speech = self.vad.is_speech(frame)
                logger.debug(f"[VAD:stream] Frame received → {'Speech' if is_speech else 'Silence'}")
                self.queue.put_nowait(is_speech)

            def __aiter__(self):
                return self

            async def __anext__(self):
                result = await self.queue.get()
                return result

        return WebRTCVADStream(self)

@dataclass
class UserData:
    ctx: Optional[JobContext] = None

class AvatarAgent(Agent):
    def __init__(self) -> None:
        chat_ctx = llm.ChatContext()

        presentation_path = Path(__file__).parent / 'presentation.json'
        with open(presentation_path, 'r', encoding='utf-8') as file:
            presentation_data = json.load(file)

        chat_ctx.add_message(
            role="system",
            content=json.dumps(presentation_data, ensure_ascii=False, indent=2),
        )

        super().__init__(
            chat_ctx=chat_ctx,
            instructions=load_prompt('prompt.yaml'),
            stt=deepgram.STT(language="ru", model="nova-2"),
            llm=openai.LLM(model="gpt-4.1-nano"),
            tts=openai.TTS(),
            vad=WebRTCVAD(),
        )

    @function_tool
    async def switchSlide(self, context: RunContext[UserData], url: str) -> None:
        userdata = context.userdata
        if not userdata.ctx or not userdata.ctx.room:
            return f"Couldn't access the room!"
        room = userdata.ctx.room
        participants = room.remote_participants
        if not participants:
            return f"No participants found!"
        participant = next(iter(participants.values()), None)
        if not participant:
            return f"Couldn't get the participant!"
        payload = {"type": "image", "url": url}
        json_payload = json.dumps(payload)
        logger.info(f"Sending switch slide payload: {json_payload}")
        await room.local_participant.perform_rpc(
            destination_identity=participant.identity,
            method="client.switchslide",
            payload=json_payload
        )
        return f"I've switched to slide: '{url}'"

    async def on_enter(self):
        await asyncio.sleep(5)
        # self.session.generate_reply()
        pass

async def entrypoint(ctx: JobContext):
    agent = AvatarAgent()
    await ctx.connect()

    userdata = UserData(ctx=ctx)
    session = AgentSession[UserData](
        userdata=userdata,
        turn_detection=MultilingualModel(),
        allow_interruptions=True,
        min_interruption_duration=0.5,
        min_endpointing_delay=0.5,
        max_endpointing_delay=6.0,
    )

    async def handle_update_settings(rpc_data):
        try:
            logger.info(f"Received update settings payload: {rpc_data}")
            payload_str = rpc_data.payload
            logger.info(f"Extracted payload string: {payload_str}")
            payload_data = json.loads(payload_str)
            logger.info(f"Parsed payload data: {payload_data}")
            audio_enabled = payload_data.get("audio")
            tracks = ctx.room.local_participant.track_publications
            for _, track in tracks.items():
                if audio_enabled:
                    track.track.unmute()
                else:
                    track.track.mute()
            for _, participant in ctx.room.remote_participants.items():
                for _, p_track in participant.track_publications.items():
                    p_track.set_subscribed(audio_enabled)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error for payload '{rpc_data.payload}': {e}")
            return f"error: {str(e)}"
        except Exception as e:
            logger.error(f"Error handling update settings: {e}")
            return f"error: {str(e)}"

    logger.info("Registering RPC methods")
    ctx.room.local_participant.register_rpc_method(
        "agent.updateSettings",
        handle_update_settings
    )

    await session.start(
        room=ctx.room,
        room_output_options=RoomOutputOptions(audio_enabled=True),
        agent=agent
    )

def load_prompt(filename):
    script_dir = os.path.dirname(os.path.abspath(__file__))
    prompt_path = os.path.join(script_dir, filename)
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            prompt_data = yaml.safe_load(file)
            return prompt_data.get('instructions', '')
    except (FileNotFoundError, yaml.YAMLError) as e:
        print(f"Error loading prompt file {filename}: {e}")
        return ""

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
