# Интерактивная презентация с ИИ-аватаром

## Обзор

Проект представляет собой интерактивное приложение для проведения презентаций с использованием технологий LiveKit и Tavus Avatar. Приложение позволяет проводить презентации с интерактивным ИИ-аватаром, который может отвечать на вопросы и управлять слайдами.

## Технологический стек

### Бэкенд
- **Python 3.9+**
- **LiveKit Agents** - для создания интерактивного агента
- **OpenAI** - для обработки естественного языка (GPT-4.1-nano)
- **Deepgram** - для распознавания речи (STT) с поддержкой русского языка
- **Tavus** - для создания и управления аватарами

### Фронтенд
- **Next.js 14+** - React-фреймворк
- **TypeScript** - для типизированного JavaScript
- **LiveKit Client** - для подключения к видеокомнатам
- **Framer Motion** - для анимаций

## Структура проекта

```
full/
├── tavus_presentation_back/     # Бэкенд на Python
│   ├── presentation.json       # Данные презентации
│   ├── prompt.yaml             # Инструкции для ИИ-агента
│   └── tavus_presentation.py   # Основной код агента
├── frontend/ # Фронтенд на Next.js
│   ├── app/
│   │   ├── api/                # API эндпоинты
│   │   └── page.tsx           # Основная страница
│   └── hooks/                 # Кастомные React-хуки
└── README.md                  # Этот файл
```

## Быстрый старт

### Предварительные требования

- Node.js 18+
- Python 3.9+
- Учетные записи для:
  - LiveKit
  - OpenAI
  - Deepgram
  - Tavus (опционально)

### Установка

1. Клонируйте репозиторий:
   ```bash
   git clone <repository-url>
   cd livekitdemo/full
   ```

2. Настройте бэкенд:
   ```bash
   cd tavus_presentation_back
   python -m venv venv
   source venv/bin/activate  # На Windows: .\venv\Scripts\activate
   pip install -r requirements.txt
   cp .env.example .env
   # Отредактируйте .env файл, добавив свои ключи API
   ```

3. Настройте фронтенд:
   ```bash
   cd ../frontend
   npm install
   cp .env.local.example .env.local
   # Отредактируйте .env.local файл, добавив настройки LiveKit
   ```

### Запуск

1. Запустите бэкенд:
   ```bash
   cd tavus_presentation_back
   source venv/bin/activate  # Если еще не активировано
   python tavus_presentation.py
   ```

2. В новом терминале запустите фронтенд:
   ```bash
   cd frontend
   npm run dev
   ```

3. Откройте http://localhost:3000 в браузере

## Функциональность

- **Интерактивный аватар**: Отвечает на вопросы голосом
- **Управление слайдами**: Переключение слайдов через RPC-вызовы
- **Поддержка русского языка**: Распознавание и синтез речи на русском
- **Адаптивный интерфейс**: Корректное отображение на разных устройствах

## Настройка презентации

1. Отредактируйте `tavus_presentation_back/presentation.json` для изменения слайдов
2. Настройте инструкции для ИИ в `tavus_presentation_back/prompt.yaml`
3. Перезапустите бэкенд для применения изменений

## Развертывание

### Продакшн

1. Настройте HTTPS для фронтенда
2. Используйте WSGI-сервер (например, Gunicorn) для бэкенда
3. Настройте переменные окружения для продакшн-среды

## Лицензия

[MIT](LICENSE)

## Контакты

По вопросам сотрудничества и поддержки обращайтесь по адресу: ваш<EMAIL>