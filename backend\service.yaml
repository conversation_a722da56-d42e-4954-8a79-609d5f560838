apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/ingress: all
  labels:
    cloud.googleapis.com/location: europe-west3
  name: presentation-agent-service
  namespace: '1097165942712'
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '40'
        run.googleapis.com/startup-cpu-boost: 'true'
      labels:
    spec:
      containerConcurrency: 80
      containers:
      - env:
        - name: OPENAI_API_KEY
          value: ********************************************************************************************************************************************************************
        - name: ELEVENLABS_API_KEY
          value: ***************************************************
        - name: DEEPGRAM_API_KEY
          value: ****************************************
        - name: TAVUS_API_KEY
          value: ********************************
        - name: LIVEKIT_URL
          value: wss://mini-65z6gds7.livekit.cloud
        - name: LIVEKIT_API_KEY
          value: APIJXFnqzzciSGc
        - name: LIVEKIT_API_SECRET
          value: aheT2W5cOfZjBvOMATFRhKaJ7lzpIjYWONG3x1oArBi
        image: europe-west3-docker.pkg.dev/presentation-462107/presentation-agent-repo/presentation-agent:main-livekitdebug-v12
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '1'
            memory: 1Gi
        startupProbe:
          failureThreshold: 12
          initialDelaySeconds: 5
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 45
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
      serviceAccountName: <EMAIL>
      timeoutSeconds: 300
  traffic:
  - latestRevision: true
    percent: 100
