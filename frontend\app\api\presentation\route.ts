import { NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs/promises';

export async function GET() {
  try {
    // Build the path to the file in the `data` directory
    const dataDir = path.join(process.cwd(), 'data');
    const filePath = path.join(dataDir, 'presentation.json');

    // Read the file content
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const jsonData = JSON.parse(fileContent);

    return NextResponse.json(jsonData);
  } catch (error) {
    console.error('Failed to read presentation data:', error);
    return NextResponse.json({ message: 'Error reading presentation data' }, { status: 500 });
  }
}
