import logging
import sys
import os
import asyncio
from flask import Flask, jsonify
from threading import Thread

from livekit.agents import Agent, JobContext, WorkerOptions, cli
from livekit.plugins.vad import VADPlugin
from livekit.plugins.openai import WhisperAPITranscriber, ChatGPTPlugin
from livekit.plugins.elevenlabs import ElevenLabsTTSPlugin

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s [%(name)s] [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("CONTEXT_AGENT_APP") # Общее имя логгера

# --- Flask App for Health Check ---
app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    logger.info("FLASK_HEALTH_CHECK: /health endpoint called")
    return jsonify(status="ok"), 200

def run_flask_app():
    port = int(os.environ.get('PORT', 8080))
    logger.info(f"FLASK_HEALTH_CHECK: Starting Flask server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)

# --- LiveKit Agent Logic ---
class ContextAgent(Agent):
    def __init__(self):
        super().__init__()
        logger.info("ContextAgent: Initializing.")
        # Инициализация плагинов будет здесь позже
        # self.vad_plugin = VADPlugin(...)
        # self.stt_plugin = WhisperAPITranscriber(...)
        # self.llm_plugin = ChatGPTPlugin(...)
        # self.tts_plugin = ElevenLabsTTSPlugin(...)
        # self.on("agent_speech_recognized", self.handle_speech_recognized)
        logger.info("ContextAgent: Initialization complete (minimal).")

    # async def handle_speech_recognized(self, text: str):
    #     logger.info(f"ContextAgent: Speech recognized: {text}")
    #     # Логика обработки распознанной речи
    #     pass

async def entrypoint(ctx: JobContext):
    logger.info("AGENT_ENTRYPOINT: Agent entrypoint called.")
    # Проверка наличия переменных окружения (можно добавить больше проверок)
    required_env_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    if missing_vars:
        logger.error(f"AGENT_ENTRYPOINT: Missing required environment variables: {', '.join(missing_vars)}")
        # Можно предпринять действия, например, завершить работу или работать с ограниченной функциональностью
        # В данном случае, продолжим, но агент, скорее всего, не сможет подключиться
    else:
        logger.info("AGENT_ENTRYPOINT: All required LiveKit environment variables seem to be present.")

    agent = ContextAgent()
    logger.info("AGENT_ENTRYPOINT: ContextAgent instance created.")
    
    # Здесь будет логика подключения агента к комнате и обработки задач
    # Например: await ctx.connect(os.environ['LIVEKIT_URL'], os.environ['LIVEKIT_API_KEY'], os.environ['LIVEKIT_API_SECRET'])
    #           await agent.run(room=ctx.room)
    logger.info("AGENT_ENTRYPOINT: Placeholder for agent connection and run logic. Entrypoint will now complete.")
    # Для начального теста, entrypoint просто завершается, позволяя cli.run_app() управлять жизненным циклом.

if __name__ == "__main__":
    logger.info(f"SCRIPT_MAIN: Starting context_test_agent.py. Args: {sys.argv}")

    # Запуск Flask в отдельном потоке
    flask_thread = Thread(target=run_flask_app, daemon=True)
    flask_thread.start()
    logger.info("SCRIPT_MAIN: Flask health check server thread started.")

    # Запуск LiveKit агента
    logger.info("SCRIPT_MAIN: About to call cli.run_app() for LiveKit agent.")
    try:
        cli.run_app(
            WorkerOptions(
                entrypoint_fnc=entrypoint,
                # Пока не указываем конкретные комнаты или идентификаторы для простоты
            )
        )
        logger.info("SCRIPT_MAIN: cli.run_app() has exited or completed.")
    except Exception as e:
        logger.error(f"SCRIPT_MAIN: An error occurred during cli.run_app(): {e}", exc_info=True)
    finally:
        logger.info("SCRIPT_MAIN: Application shutdown sequence complete.")
