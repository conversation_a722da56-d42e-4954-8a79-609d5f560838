'use client';

import React, { useState } from 'react';
import { usePresentationStore } from '../store/presentationStore';
import Image from 'next/image';

interface IntroPanelProps {
  onStart: (language: string) => void;
  isLoading: boolean;
  name: string;
  description: string;
  coverUrl: string;
}

const translations = {
  de: {
    title: 'Lu<PERSON><PERSON><PERSON><PERSON> Wohnung mit Blick auf den Main',
    description: '<PERSON><PERSON> und herzlich willkommen zu Ihrer virtuellen Besichtigung. Ich begleite Si<PERSON> dabei und stelle Ihnen alle wichtigen Bereiche vor.',
    selectLanguage: 'Sprache auswählen:',
    connect: 'Verbinden',
  },
  en: {
    title: 'Luxurious Apartment with a View of the Main River',
    description: 'Hello and welcome to your virtual tour. I will guide you and show you all the important areas.',
    selectLanguage: 'Select language:',
    connect: 'Connect',
  },
  ru: {
    title: 'Роскошная квартира с видом на реку Майн',
    description: 'Здравствуйте и добро пожаловать в ваш виртуальный тур. Я буду вашим гидом и покажу все важные зоны.',
    selectLanguage: 'Выберите язык:',
    connect: 'Подключиться',
  },
  he: {
    title: 'דירה יוקרתית עם נוף לנהר המיין',
    description: 'שלום וברוכים הבאים לסיור הווירטואלי שלכם. אני אדריך אתכם ואציג את כל האזורים החשובים.',
    selectLanguage: 'בחר שפה:',
    connect: 'התחבר',
  },
};

const languages = [
  { code: 'de', name: 'Deutsch' },
  { code: 'en', name: 'English' },
  { code: 'ru', name: 'Русский' },
  { code: 'he', name: 'עברית' },
];

export default function IntroPanel({ onStart, isLoading, name, description, coverUrl }: IntroPanelProps) {
  const [language, setLanguage] = useState('de');
  const { startPresentation } = usePresentationStore();

  const t = translations[language as keyof typeof translations];
  
  const handleStart = (lang: string) => {
    onStart(lang);
    // Запускаем презентацию после подключения
    startPresentation();
  };

  return (
    <div className="flex flex-col items-center justify-center text-center p-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-4 overflow-hidden rounded-lg shadow-lg">
          <Image src={coverUrl} alt={name} width={1024} height={576} />
        </div>
        <h1 className="text-4xl font-bold mb-2">{t.title}</h1>
        <p className="text-lg text-gray-600 mb-8">{t.description}</p>
      </div>

      <div className="flex flex-col items-center space-y-4">
        <p className="text-lg font-medium text-gray-700">{t.selectLanguage}</p>
        <div className="mb-4">
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            className="px-6 py-3 rounded-lg text-lg shadow bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {languages.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.name}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => handleStart(language)}
          disabled={isLoading}
          className="px-8 py-4 bg-green-600 text-white rounded-lg text-xl shadow-lg hover:bg-green-700 transition transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Connecting...' : t.connect}
        </button>
      </div>
    </div>
  );
}
