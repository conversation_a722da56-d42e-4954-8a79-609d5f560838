'use client';

import { useEffect } from 'react';
import { usePresentationStore } from '@/store/presentationStore';
import { Presentation } from '@/lib/types';

export const usePresentationLoader = () => {
  const setPresentation = usePresentationStore((state) => state.setPresentation);

  useEffect(() => {
    const fetchPresentation = async () => {
      try {
        const response = await fetch('/api/presentation');
        if (!response.ok) {
          throw new Error('Failed to fetch presentation data');
        }
        const data = await response.json();
        setPresentation({
          name: data.name || '',
          description: data.description || '',
          cover: data.cover || '',
          slides: data.slides || [],
        });
      } catch (error) {
        console.error('Error loading presentation:', error);
      }
    };

    fetchPresentation();
  }, [setPresentation]);
};
