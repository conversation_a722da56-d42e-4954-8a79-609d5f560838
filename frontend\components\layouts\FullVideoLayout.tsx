import React from 'react';
import { LayoutProps } from './types';

const FullVideoLayout: React.FC<LayoutProps> = ({ slide }) => {
  if (!slide.videoUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-red-100">
        <p className="text-red-500">Error: Video URL is missing for this slide.</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-center justify-center bg-black">
      <video
        className="w-full h-full object-contain"
        src={slide.videoUrl}
        controls
        autoPlay
        muted // Important for autoplay in many browsers
      >
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default FullVideoLayout;
