import { usePresentationStore } from "@/store/presentationStore";
import { useShallow } from 'zustand/react/shallow';

export function useConnectionLogic() {
  const {
    serverUrl,
    token,
    isConnecting,
    connectionError,
    connect,
    disconnect,
  } = usePresentationStore(
    useShallow((state) => ({
      serverUrl: state.serverUrl,
      token: state.token,
      isConnecting: state.isConnecting,
      connectionError: state.connectionError,
      connect: state.connect,
      disconnect: state.disconnect,
    }))
  );

  return {
    serverUrl,
    token,
    isConnecting,
    connectionError,
    onConnectButtonClicked: connect,
    onDisconnected: disconnect,
  };
}
