import { AnimatePresence } from "motion/react";
import {
  useVoiceAssistant,
} from "@livekit/components-react";
// import { CloseIcon } from "./CloseIcon";
import SlideControls from "./SlideControls";
import { AgentMutedIcon, AgentUnmutedIcon, CloseIcon } from "./icons";
import UserVoiceControlBar from './UserVoiceControlBar';


export default function ControlBar(props: {
  onConnectButtonClicked: () => void;
  toggleChat: () => Promise<void>;
  chatOpened: boolean;
}) {
  
  const { state: agentState } = useVoiceAssistant();

  return (
    <div className="flex h-[60px] items-center">
      <AnimatePresence>
        {agentState === "disconnected" ? (
          <button
            className="uppercase absolute left-1/2 -translate-x-1/2 px-4 py-2 bg-white text-black rounded-md"
            onClick={() => props.onConnectButtonClicked()}
          >
            Начать презентацию
          </button>
        ) : (
          <div className="flex  items-center justify-center gap-2 lg:gap-6 h-auto py-1 px-8 bg-black opacity-70 rounded-full h-[20px] lg:h-[45px]">
            <UserVoiceControlBar controls={{ leave: false, microphone: !props.chatOpened }} />
            {/* Панель управления слайдами */}

            <div className="flex items-center">
              <button
                type="button"
                onClick={async () => props.toggleChat()}
                className={`flex items-center justify-center cursor-pointer transition-all duration-200 border-none outline-none`}
                aria-label={props.chatOpened ? "Закрыть чат" : "Открыть чат"}
              >
                {props.chatOpened ? (
                  <AgentMutedIcon className="h-[12px] lg:h-[29px]" />
                ) : (
                  <AgentUnmutedIcon className="h-[12px] lg:h-[29px]"/>
                )}
                
              </button>
            </div>
            <SlideControls />
          </div>
        )}
      </AnimatePresence>
    </div>
  );
}
