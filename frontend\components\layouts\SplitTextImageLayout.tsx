import React from 'react';
import Image from 'next/image';
import { LayoutProps } from './types';
import styles from './SplitTextImageLayout.module.css';

const SplitTextImageLayout: React.FC<LayoutProps> = ({ slide }) => {
  return (
    <div className="w-full h-full grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
      <div className="flex flex-col justify-center">
        <h2 className="text-3xl font-bold mb-4 text-black">{slide.title}</h2>
        {slide.technical_description && (
          <p className="text-lg text-gray-600 mb-4">{slide.technical_description}</p>
        )}
        <p className="text-gray-700">{slide.presentation_text}</p>
      </div>
      <div className="relative w-full h-full">
        {slide.type === 'video' ? (
          <div style={{
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            borderRadius: '0.5rem',
            position: 'relative'
          }}>
            <video
              src={slide.url}
              autoPlay
              muted
              loop
              playsInline
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: '0.5rem'
              }}
            />
          </div>
        ) : (
          <div className={styles.mediaContainer}>
            <div className={styles.mediaWrapper}>
              <Image
                src={slide.url}
                alt={slide.title}
                fill
                className={styles.mediaImage}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SplitTextImageLayout;
