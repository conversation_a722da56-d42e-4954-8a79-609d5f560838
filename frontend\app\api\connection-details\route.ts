import { AccessToken, AccessTokenOptions, VideoGrant } from "livekit-server-sdk";
import { NextResponse } from "next/server";
import type { ConnectionDetails } from "@/lib/types";

// NOTE: you are expected to define the following environment variables in `.env.local`:
const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.LIVEKIT_URL;
const AGENT_URL = process.env.NEXT_PUBLIC_AGENT_URL; // <--- НОВАЯ ПЕРЕМЕННАЯ

// don't cache the results
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    if (LIVEKIT_URL === undefined) {
      throw new Error("LIVEKIT_URL is not defined");
    }
    if (API_KEY === undefined) {
      throw new Error("LIVEKIT_API_KEY is not defined");
    }
    if (API_SECRET === undefined) {
      throw new Error("LIVEKIT_API_SECRET is not defined");
    }
    if (AGENT_URL === undefined) { // <--- ПРОВЕРКА НОВОЙ ПЕРЕМЕННОЙ
      throw new Error("NEXT_PUBLIC_AGENT_URL is not defined");
    }

    // Extract room_name, participant_identity, and metadata from request body
    const { room_name: roomName, participant_identity: participantIdentity, metadata } = await req.json();

    if (!roomName || !participantIdentity) {
      return NextResponse.json({ error: 'room_name and participant_identity are required' }, { status: 400 });
    }

    // Generate participant token with metadata
    const participantToken = await createParticipantToken(
      { identity: participantIdentity, metadata },
      roomName
    );

    // <--- НАЧАЛО: ВЫЗОВ /init-agent (ВРЕМЕННО ОТКЛЮЧЕНО) --- 
    // try {
    //   const initAgentResponse = await fetch(`${AGENT_URL}/init-agent`, {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify({
    //       room_name: roomName,
    //       participant_identity: participantIdentity,
    //       // Если ваш /init-agent ожидает другие или дополнительные параметры,
    //       // их нужно будет добавить здесь.
    //     }),
    //   });

    //   if (!initAgentResponse.ok) {
    //     const errorText = await initAgentResponse.text();
    //     console.error(`Error calling /init-agent: ${initAgentResponse.status} ${initAgentResponse.statusText}`, errorText);
    //     // Решите, должна ли ошибка инициализации агента прерывать получение токена.
    //     // Пока мы просто логируем ошибку и продолжаем.
    //   } else {
    //     console.log(`/init-agent called successfully for room: ${roomName}`);
    //   }
    // } catch (agentError) {
    //   console.error("Failed to fetch /init-agent:", agentError);
    //   // Аналогично, обрабатываем ошибку сети и продолжаем.
    // }
    // --- КОНЕЦ: ВЫЗОВ /init-agent --->

    // Return connection details
    const data: ConnectionDetails = {
      serverUrl: LIVEKIT_URL,
      roomName,
      participantToken: participantToken,
      participantName: participantIdentity,
    };
    const headers = new Headers({
      "Cache-Control": "no-store",
    });
    return NextResponse.json(data, { headers });
  } catch (error) {
    if (error instanceof Error) {
      console.error(error);
      return new NextResponse(error.message, { status: 500 });
    }
    // Handle non-Error objects if necessary
    return new NextResponse("An unexpected error occurred", { status: 500 });
  }
}

function createParticipantToken(userInfo: AccessTokenOptions, roomName: string) {
  if (!API_KEY || !API_SECRET) { // Добавим проверку, чтобы TypeScript был доволен
    throw new Error("API_KEY or API_SECRET is not defined for token creation");
  }
  const at = new AccessToken(API_KEY, API_SECRET, {
    ...userInfo,
    ttl: "15m",
  });
  const grant: VideoGrant = {
    room: roomName,
    roomJoin: true,
    canPublish: true,
    canPublishData: true,
    canSubscribe: true,
  };
  at.addGrant(grant);
  return at.toJwt();
}
