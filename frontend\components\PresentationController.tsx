'use client';

import { useEffect } from 'react';
import { useRoomContext } from '@livekit/components-react';
import { type RpcInvocationData } from 'livekit-client';
import { usePresentationStore } from '@/store/presentationStore';

const PresentationController = () => {
  const room = useRoomContext();
  const { setCurrentSlideIndex, slides } = usePresentationStore();

  useEffect(() => {
    if (!room) return;

    const handleSwitchSlide = async (data: RpcInvocationData) => {
      console.log('Received raw RPC data object:', data);
      console.log('Received raw RPC data.payload:', data.payload);
      try {
        const payload = JSON.parse(data.payload);
        const newIndex = payload.index;

        if (typeof newIndex === 'number' && newIndex >= 0 && newIndex < slides.length) {
          console.log(`Switching to slide index: ${newIndex}`);
          setCurrentSlideIndex(newIndex);
          return 'Success';
        } else {
          console.error('Invalid slide index received:', newIndex);
          return 'Error: Invalid index';
        }
      } catch (e) {
        console.error('Failed to parse RPC payload:', e);
        return 'Error: Invalid payload';
      }
    };

    room.registerRpcMethod('client.switchslide', handleSwitchSlide);

    return () => {
      room.unregisterRpcMethod('client.switchslide');
    };
  }, [room, setCurrentSlideIndex, slides]);

  return null; // This component does not render anything
};

export default PresentationController;
