import React from 'react';
import Image from 'next/image';
import { LayoutProps } from './types';

const FullImageLayout: React.FC<LayoutProps> = ({ slide }) => {
  return (
    <div className="relative w-full h-full">
      <Image
        src={slide.url}
        alt={slide.title}
        fill
        sizes="100vw"
        style={{ objectFit: 'contain' }}
        className="rounded-lg"
      />
    </div>
  );
};

export default FullImageLayout;
