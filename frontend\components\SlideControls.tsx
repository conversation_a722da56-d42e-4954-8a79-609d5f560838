'use client';

import { useState } from 'react';
import { usePresentationStore } from '@/store/presentationStore';
import { useShallow } from 'zustand/react/shallow';
import { useChat } from '@livekit/components-react';
import { PreviousSlideIcon, PreviousSlideDisabledIcon, NextSlideIcon, NextSlideDisabledIcon } from "./icons";

export default function SlideControls() {
  const { send } = useChat();
  
  const { 
    currentSlideIndex, 
    slides
  } = usePresentationStore(
    useShallow((state) => ({
      currentSlideIndex: state.currentSlideIndex,
      slides: state.slides,
    }))
  );

  const sendCommand = async (command: string) => {
    if (send) {
      await send(command);
      console.log(`Sent command: ${command}`);
    }
  };

  const handlePrevSlide = () => {
    if (currentSlideIndex > 0) {
      sendCommand('предыдущий слайд');
    }
  };

  const handleNextSlide = () => {
    if (currentSlideIndex < slides.length - 1) {
      sendCommand('следующий слайд');
    }
  };

  



  const canGoPrev = currentSlideIndex > 0;
  const canGoNext = currentSlideIndex < slides.length - 1;

  return (
    <div className="flex flex-row items-center gap-2 lg:gap-6 rounded-lg">
            
      {/* Предыдущий слайд */}
      <button
        onClick={handlePrevSlide}
        disabled={!canGoPrev}
        className={`flex items-center justify-center transition-all duration-200`}
        aria-label="Предыдущий слайд"
      >
        {canGoPrev ? (
          <PreviousSlideIcon className="h-[12px] lg:h-[29px]" />
        ) : (
          <PreviousSlideDisabledIcon className="h-[12px] lg:h-[29px]" />
        )}
      </button>

      {/* Следующий слайд */}
      <button
        onClick={handleNextSlide}
        disabled={!canGoNext}
        className={`flex items-center justify-center transition-all duration-200`}
        aria-label="Следующий слайд"
      >
        {canGoNext ? (
          <NextSlideIcon className="h-[12px] lg:h-[29px]" />
        ) : (
          <NextSlideDisabledIcon className="h-[12px] lg:h-[29px]" />
        )}
      </button>

      {/* Индикатор текущего слайда */}
      {/* <div className="ml-2 text-sm text-gray-300">
        {currentSlideIndex + 1} / {slides.length}
      </div> */}
    </div>
  );
}
