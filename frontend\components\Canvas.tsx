'use client';

import React from 'react';
import { usePresentationStore } from '@/store/presentationStore';
import { layoutRegistry } from '@/lib/layoutRegistry';
import { LayoutVariant } from './layouts/types';

const Canvas = () => {
  const { slides, currentSlideIndex } = usePresentationStore();

  const currentSlide = slides[currentSlideIndex];

  if (!currentSlide) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <p className="text-gray-500">Loading presentation...</p>
      </div>
    );
  }

  const LayoutComponent = layoutRegistry[currentSlide.layout as LayoutVariant];

  if (!LayoutComponent) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-red-100">
        <p className="text-red-500">Error: Layout &quot;{currentSlide.layout}&quot; not found!</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full p-4 ">
      <LayoutComponent slide={currentSlide} />
    </div>
  );
};

export default Canvas;

