"use client";

import React, { useState } from "react";
import { usePresentationLoader } from "@/hooks/usePresentationLoader";
import { useConnectionLogic } from "@/hooks/useConnectionLogic";
import { usePresentationStore } from "@/store/presentationStore";
import { LiveKitRoom } from "@livekit/components-react";
import VoiceAssistantShell from "@/components/VoiceAssistantShell";
import IntroPanel from "@/components/IntroPanel";
import { BackgroundMusic } from "@/components/BackgroundMusic";

export default function Page() {
  usePresentationLoader();
  const [showIntro, setShowIntro] = useState(true);
  const { serverUrl, token, onDisconnected } = useConnectionLogic();
  // Получаем intro-данные из стора (добавь в store, если нет)
  const { name, description, cover } = usePresentationStore();
  
  // Добавляем компонент BackgroundMusic, который будет доступен на всех страницах

  const { connect, isConnecting } = usePresentationStore();

  const handleStart = async (language: string) => {
    setShowIntro(false);
    await connect(language); // Запуск соединения с выбранным языком
  };

  if (showIntro && name && description) {
    return (
      <main className="flex h-screen flex-col items-center justify-center">
        <IntroPanel
          name={name}
          description={description}
          coverUrl={cover}
          onStart={handleStart}
          isLoading={isConnecting}
        />
      </main>
    );
  }

  return (
    <main data-lk-theme="default" className="flex h-screen flex-col justify-center relative">
      {/* Компонент фоновой музыки, который будет доступен на всех страницах */}
      <BackgroundMusic />
      
      {isConnecting && (
        <div className="absolute inset-0 z-20 flex items-center justify-center bg-white/70">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-gray-900" />
        </div>
      )}
      <LiveKitRoom
        className="flex-grow w-full bg-gradient-to-b from-background to-foreground"
        serverUrl={serverUrl}
        token={token}
        connect={!!(serverUrl && token)}
        onDisconnected={onDisconnected}
      >
        <VoiceAssistantShell />
      </LiveKitRoom>
    </main>
  );
}

