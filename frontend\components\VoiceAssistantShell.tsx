import { useState } from "react";
import { useVoiceAssistant, useRoomContext } from "@livekit/components-react";
import { usePresentationStore } from "@/store/presentationStore";
import { useShallow } from "zustand/react/shallow";
import DisconnectedPanel from "./DisconnectedPanel";
import ConnectedPanel from "./ConnectedPanel";

export default function VoiceAssistantShell() {
  const room = useRoomContext();
  const { state: agentState, agent } = useVoiceAssistant();
  const [showChat, setShowChat] = useState(false);

  const { isConnecting, connectionError, connect } = usePresentationStore(
    useShallow((state) => ({
      connect: state.connect,
      isConnecting: state.isConnecting,
      connectionError: state.connectionError,
    }))
  );
  
  // Обертываем функцию connect, чтобы она не требовала параметров
  const onConnectButtonClicked = () => {
    // Используем язык по умолчанию 'de'
    connect('de');
  };

  async function toggleChat(): Promise<void> {
    setShowChat((prev) => !prev);
    if (!agent || !room) return;
    const payload = { audio: showChat };
    await room.localParticipant.performRpc({
      destinationIdentity: agent.identity,
      method: "agent.updateSettings",
      payload: JSON.stringify(payload),
    });
  }

  if (agentState === "disconnected") {
    return (
      <DisconnectedPanel
        onConnectButtonClicked={onConnectButtonClicked}
        isConnecting={isConnecting}
        connectionError={connectionError || undefined}
      />
    );
  }

  return (
    <ConnectedPanel
      showChat={showChat}
      toggleChat={toggleChat}
      onConnectButtonClicked={onConnectButtonClicked}
    />
  );
}
