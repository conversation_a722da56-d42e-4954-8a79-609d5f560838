{"name": "LiveKit Tavus Agent", "description": "Агент LiveKit с интеграцией Tavus для создания аватаров", "version": "1.0.0", "dependencies": {"python": ">=3.8", "packages": ["livekit-agents~=1.0", "python-dotenv", "requests>=2.32.0", "annoy", "pydantic", "flask", "pandas", "websockets>=11.0.3", "rich", "mcp"]}, "features": ["openai", "silero", "turn-detector", "deepgram", "google", "anthropic", "cartesia", "elevenlabs", "rime", "playai", "groq", "tavus"], "environment": {"requiresEnvFile": true}, "execution": {"entrypoint": "tavus.py"}}