import asyncio
import logging
import os
import threading
from flask import Flask, jsonify
from livekit.agents import JobContext, WorkerOptions, cli




# --- Logging Setup ---
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO").upper()
JSON_LOGS = os.environ.get("JSON_LOGS", "false").lower() == "true"

logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s %(levelname)s [%(name)s] [%(filename)s:%(lineno)d] - %(message)s"
    if not JSON_LOGS
    else None,
)
logger = logging.getLogger("MINIMAL_AGENT")

# --- Agent Readiness ---
agent_ready = False
livekit_agent_initialized = False

# --- Flask Health Check Server ---
flask_app = Flask(__name__)
FLASK_PORT = int(os.environ.get("PORT", 8080))

@flask_app.route("/health")
def health_check():
    logger.info("MINIMAL_FLASK: /health endpoint called.")
    return "OK", 200

@flask_app.route("/init-agent")
def init_agent_status():
    logger.info("MINIMAL_FLASK: /init-agent endpoint called.")
    if livekit_agent_initialized:
        return jsonify({"status": "LiveKit Agent Initialized"}), 200
    else:
        return jsonify({"status": "LiveKit Agent Not Yet Initialized"}), 503

def run_flask_server():
    global agent_ready
    logger.info(f"MINIMAL_FLASK: Flask server attempting to run on port {FLASK_PORT}")
    try:
        flask_app.run(host="0.0.0.0", port=FLASK_PORT, debug=False, use_reloader=False)
        agent_ready = True # Flask server is up
        logger.info(f"MINIMAL_FLASK: Flask server started and listening on port {FLASK_PORT}")
    except Exception as e:
        logger.error(f"MINIMAL_FLASK: Failed to start Flask server: {e}", exc_info=True)
        # Potentially exit or signal failure if Flask is critical for health checks
        # For now, just log, as the main agent might still run or have its own issues.

# --- Minimal LiveKit Agent Entrypoint ---
async def minimal_entrypoint(ctx: JobContext):
    global livekit_agent_initialized
    logger.info("MINIMAL_AGENT_ENTRYPOINT: Entrypoint function started.")
    
    # Simulate some work or connection
    try:
        # In a real minimal agent that needs to connect:
        # logger.info("MINIMAL_AGENT_ENTRYPOINT: Attempting to connect to LiveKit room...")
        # await ctx.connect() # This would require LIVEKIT_URL, API_KEY, API_SECRET
        # logger.info("MINIMAL_AGENT_ENTRYPOINT: Successfully connected to LiveKit server.")
        
        # For this super-minimal test, we'll just log and set the flag
        await asyncio.sleep(1) # Simulate some async work
        livekit_agent_initialized = True
        logger.info("MINIMAL_AGENT_ENTRYPOINT: LiveKit agent simulated initialization complete.")
        
        # Keep the agent running indefinitely or until job is done
        while True:
            await asyncio.sleep(60) # Keep alive
            logger.info("MINIMAL_AGENT_ENTRYPOINT: Agent is alive...")

    except Exception as e:
        logger.error(f"MINIMAL_AGENT_ENTRYPOINT: Error in entrypoint: {e}", exc_info=True)
        livekit_agent_initialized = False # Mark as not initialized on error
    finally:
        logger.info("MINIMAL_AGENT_ENTRYPOINT: Entrypoint function finished or exited.")


if __name__ == "__main__":
    logger.info("MINIMAL_AGENT_MAIN: Script starting.")

    required_env_vars = ["LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    if missing_vars:
        logger.warning(f"MINIMAL_AGENT_MAIN: Missing environment variables: {', '.join(missing_vars)}. Agent might not connect if connection is attempted.")
    else:
        logger.info("MINIMAL_AGENT_MAIN: All required LiveKit environment variables seem to be present.")

    # Start Flask server in a daemon thread
    logger.info("MINIMAL_AGENT_MAIN: Starting Flask server in a background thread.")
    flask_thread = threading.Thread(target=run_flask_server, daemon=True)
    flask_thread.start()

    # Give Flask a moment to start, though Cloud Run health checks will also wait
    # This is more for local testing or very fast agent exits.
    # time.sleep(2) # Not strictly necessary for Cloud Run

    logger.info("MINIMAL_AGENT_MAIN: About to call cli.run_app() for the minimal agent.")
    try:
        cli.run_app(
            WorkerOptions(
                entrypoint_fnc=minimal_entrypoint,
                # Set other WorkerOptions if necessary, e.g., log_level
            )
        )
    except Exception as e:
        logger.error(f"MINIMAL_AGENT_MAIN: cli.run_app() failed: {e}", exc_info=True)
    finally:
        logger.info("MINIMAL_AGENT_MAIN: cli.run_app() has exited or completed.")
        # Wait for Flask thread to finish if it's not a daemon or if cleanup is needed
        # Since it's a daemon, it will exit when the main thread exits.
