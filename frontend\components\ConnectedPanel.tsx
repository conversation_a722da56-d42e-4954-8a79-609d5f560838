import Canvas from "./Canvas";
import PresentationController from "./PresentationController";
import { Disconnect<PERSON><PERSON>on, RoomAudioRenderer, useVoiceAssistant } from "@livekit/components-react";
import { NoAgentNotification } from "./NoAgentNotification";
import ChatSidebar from "./ChatSidebar";
import ControlBar from "./ControlBar";

import { CloseIcon } from "./icons";

export default function ConnectedPanel({
  showChat,
  toggleChat,
  onConnectButtonClicked,
}: {
  showChat: boolean;
  toggleChat: () => Promise<void>;
  onConnectButtonClicked: () => void;
}) {
  const { state: agentState } = useVoiceAssistant();
  return (
    <div className="flex flex-col h-full w-full relative">
      <div className="flex flex-col md:flex-row gap-4 flex-grow min-h-0">
        <div className="flex flex-col flex-1 w-full md:flex-initial md:h-auto relative min-h-0">
          <div className="w-full h-full flex-1 flex flex-col min-h-0 overflow-y-auto">
            <Canvas />
            <PresentationController />
          </div>
          <RoomAudioRenderer />
          <NoAgentNotification state={agentState} />
        </div>
        <ChatSidebar visible={showChat} />
      </div>
      <div className="absolute bottom-0 lg:bottom-12 left-1/2 transform -translate-x-1/2 z-10">
        <ControlBar
          onConnectButtonClicked={onConnectButtonClicked}
          toggleChat={toggleChat}
          chatOpened={showChat}
        />
      </div>
      <DisconnectButton className="!absolute top-3 lg:top-4 right-4 w-[24px] h-[24px] lg:w-[40px] !bg-transparent !border-none">
        <CloseIcon className="h-[12px] lg:h-[40px]" />
      </DisconnectButton>

    </div>
  );
}
