import { create } from 'zustand';
import { AgentState, Slide, Viewport, ConnectionDetails, PresentationState } from '@/lib/types';

export const usePresentationStore = create<PresentationState>((set, get) => ({
  slides: [],
  viewport: { x: 0, y: 0, zoom: 1 },
  currentSlideIndex: -1, // Начинаем с -1 для интро-экрана
  agentState: 'idle',
  audioEnabled: true,
  language: 'en',
  name: '',
  description: '',
  cover: '',
  serverUrl: undefined,
  token: undefined,
  isConnecting: false,
  connectionError: null,
  isPlaying: false, // Добавлено

  setSlides: (slides) => set({ slides }),

  setLanguage: (language) => set({ language }),

  setPresentation: ({ name, description, cover, slides }) => set({ name, description, cover, slides }),
  setViewport: (viewport) => set((state) => ({ viewport: { ...state.viewport, ...viewport } })),
  setCurrentSlideIndex: (index) => {
    const { slides } = get();
    if (index >= 0 && index < slides.length) {
      set({ currentSlideIndex: index, isPlaying: true }); // Обновлено
    }
  },
  setAgentState: (agentState) => set({ agentState }),
  setAudioEnabled: (audioEnabled) => set({ audioEnabled }),

  startPresentation: () => set({ isPlaying: true, currentSlideIndex: 0 }), // Добавлено

  goToNextSlide: () => {
    const { slides, currentSlideIndex } = get();
    if (currentSlideIndex < slides.length - 1) {
      set({ currentSlideIndex: currentSlideIndex + 1, isPlaying: true }); // Обновлено
    }
  },

  goToPrevSlide: () => {
    const { currentSlideIndex } = get();
    if (currentSlideIndex > 0) {
      set({ currentSlideIndex: currentSlideIndex - 1, isPlaying: true }); // Обновлено
    }
  },

  // Connection actions
  connect: async (language: string) => {
    set({ isConnecting: true, connectionError: null, language });
    try {
      // Используем относительный URL для обращения к API на том же домене (Vercel)
      const connectionDetailsUrl = `/api/connection-details`;

      const roomName = `room-${Math.random().toString(36).substring(7)}`;
      const participantIdentity = `participant-${Math.random().toString(36).substring(7)}`;
      const response = await fetch(connectionDetailsUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          room_name: roomName,
          participant_identity: participantIdentity,
          metadata: JSON.stringify({ language }),
        }),
      });
      if (!response.ok) {
        let errorMsg = `Failed to get connection details: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorData.message || errorMsg;
        } catch { /* Ignore */ }
        throw new Error(errorMsg);
      }
      const connectionDetailsData: ConnectionDetails = await response.json();
      set({
        serverUrl: connectionDetailsData.serverUrl,
        token: connectionDetailsData.participantToken,
      });
    } catch (e: unknown) {
      console.error("Connection failed:", e);
      const errorMessage = e instanceof Error ? e.message : "An unknown error occurred during connection.";
      set({ connectionError: errorMessage });
    } finally {
      set({ isConnecting: false });
    }
  },

  disconnect: () => {
    set({ serverUrl: undefined, token: undefined, isPlaying: false }); // Обновлено
  },
}));
