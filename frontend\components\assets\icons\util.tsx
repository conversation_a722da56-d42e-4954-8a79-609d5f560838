import * as React from 'react';
import { Track } from 'livekit-client';
import { MicrophoneIcon, MicrophoneDisabledIcon } from '@/components/icons';

/**
 * @internal
 */
export function getSourceIcon(source: Track.Source, enabled: boolean) {
  switch (source) {
    case Track.Source.Microphone:
      // return enabled ? <MicIcon /> : <MicDisabledIcon />;
      return enabled ? <MicrophoneIcon className="lg:h-[16px] lg:w-[16px] h-[10px] w-[10px]" /> : <MicrophoneDisabledIcon className="lg:h-[16px] lg:w-[16px] h-[10px] w-[10px]" />;
    case Track.Source.Camera:
      // Для камеры используем простые иконки
      return enabled ? (
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M0 5a2 2 0 0 1 2-2h7.5a2 2 0 0 1 1.983 1.738l3.11-1.382A1 1 0 0 1 16 4.269v7.462a1 1 0 0 1-1.406.913l-3.111-1.382A2 2 0 0 1 9.5 13H2a2 2 0 0 1-2-2V5z"/>
        </svg>
      ) : (
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M13.961 1.61a.5.5 0 0 0-.707 0L9.5 5.364 5.364 1.228a.5.5 0 0 0-.707.707L8.793 6.071 1.146 13.718a.5.5 0 1 0 .708.707l6.647-6.647 4.136 4.136a.5.5 0 0 0 .707-.707L9.207 6.071l4.136-4.136a.5.5 0 0 0 0-.707z"/>
        </svg>
      );
    case Track.Source.ScreenShare:
      return enabled ? (
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M13.5 1a1.5 1.5 0 0 1 1.5 1.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 9.5v-7A1.5 1.5 0 0 1 2.5 1h11zm-11-1A2.5 2.5 0 0 0 0 2.5v7A2.5 2.5 0 0 0 2.5 12h11a2.5 2.5 0 0 0 2.5-2.5v-7A2.5 2.5 0 0 0 13.5 0h-11z"/>
          <path d="M11 14a1 1 0 1 1 0-2h1a1 1 0 1 1 0 2h-1zm-9 0a1 1 0 1 1 0-2h1a1 1 0 1 1 0 2H2z"/>
        </svg>
      ) : (
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M13.5 1a1.5 1.5 0 0 1 1.5 1.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 9.5v-7A1.5 1.5 0 0 1 2.5 1h11z"/>
        </svg>
      );
    default:
      return undefined;
  }
}
