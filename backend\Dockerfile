# Этап 1: Сборщик с uv для установки зависимостей
FROM python:3.13-slim AS builder

# Устанавливаем системные зависимости для сборки (включая g++)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    python3-dev && \
    rm -rf /var/lib/apt/lists/*

# Устанавливаем uv
RUN pip install uv

WORKDIR /app

# Копируем только requirements.txt для кеширования зависимостей
COPY requirements.txt .

# Устанавливаем зависимости с помощью uv в системный Python этого этапа
# --system устанавливает пакеты в стандартные site-packages, что упрощает их копирование
RUN uv pip install --system Cython
RUN uv pip install --system -r requirements.txt

# Этап 2: Финальный образ
FROM python:3.13-slim AS final

WORKDIR /app

# Копируем установленные зависимости из сборщика
# Путь к site-packages зависит от версии Python
COPY --from=builder /usr/local/lib/python3.13/site-packages /usr/local/lib/python3.13/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Копируем остальной код приложения
COPY . .

# Выставляем порт (Cloud Run все равно будет использовать PORT из окружения, но это хорошая практика)
EXPOSE 8080

# Устанавливаем переменную окружения PORT, которую ожидает Cloud Run
# livekit-agents должен автоматически подхватывать эту переменную для своего сервера.
ENV PORT=8080
ENV PYTHONUNBUFFERED=1

# Запускаем приложение в production режиме
CMD ["python", "presentation.py", "start"]
