'use client';

import { useEffect, useRef } from 'react';
import { usePresentationStore } from '../store/presentationStore';

export const BackgroundMusic = () => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const { isPlaying } = usePresentationStore();

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = async () => {
      try {
        // Устанавливаем громкость на 15%
        audio.volume = 0.15;
        await audio.play();
      } catch (err) {
        console.log('Auto-play was prevented. Waiting for user interaction.');
        const handleFirstInteraction = () => {
          // Устанавливаем громкость на 15% и для этого случая
          audio.volume = 0.15;
          audio.play().catch(console.error);
          document.removeEventListener('click', handleFirstInteraction, true);
          document.removeEventListener('touchstart', handleFirstInteraction, true);
        };

        document.addEventListener('click', handleFirstInteraction, true);
        document.addEventListener('touchstart', handleFirstInteraction, true);

        // Возвращаем функцию очистки, чтобы удалить слушатели, если компонент размонтируется
        return () => {
            document.removeEventListener('click', handleFirstInteraction, true);
            document.removeEventListener('touchstart', handleFirstInteraction, true);
        }
      }
      return () => {};
    };

    let cleanupInteraction: () => void = () => {};

    if (isPlaying) {
      handlePlay().then(cleanup => {
        cleanupInteraction = cleanup;
      });
    } else {
      audio.pause();
    }

    return () => {
        cleanupInteraction();
    }
  }, [isPlaying]);

  return (
    <audio 
      ref={audioRef} 
      loop 
      src="/02/lofi-background.mp3"
      preload="auto"
      className="hidden"
    />
  );
};
