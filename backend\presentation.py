import logging
import json
import os
import yaml
import asyncio
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, List, Dict, Any
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, llm, RoomOutputOptions
from livekit.agents.llm import function_tool
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.plugins import openai, silero, deepgram
from aiohttp import web
import aiohttp_cors

load_dotenv('.env')

logger = logging.getLogger("presentation_agent")
logger.setLevel(logging.INFO)

@dataclass
class UserData:
    """Class to store user data during a session."""
    ctx: Optional[JobContext] = None
    slides: List[Dict[str, Any]] = field(default_factory=list)
    current_slide_index: int = 0
    language: str = 'en'  # Add language field with a default


class PresentationAgent(Agent):
    def __init__(self, instructions: str, chat_ctx: llm.ChatContext) -> None:
        super().__init__(
            tts=openai.TTS(
                voice="nova",
                instructions=load_prompt('prompt_tts.yaml', 'tts_instructions'),
            ),
            llm=openai.LLM(model="gpt-4.1-mini",temperature=0.4),
            instructions=instructions,
            chat_ctx=chat_ctx, # The chat context is now prepared outside and passed in
            stt=openai.STT(),
            vad=silero.VAD.load(min_silence_duration=0.3),
        )
        self.current_tts_task = None  # Для управления задачей синтеза речи
        self.last_switch_time = 0
        self.switch_cooldown_seconds = 3 # Cooldown of 3 seconds

    async def speak_text(self, text, context):
        # Перед запуском новой задачи отменяем предыдущую
        if self.current_tts_task and not self.current_tts_task.done():
            self.current_tts_task.cancel()
            try:
                await self.current_tts_task
            except asyncio.CancelledError:
                pass
        # Запускаем новую задачу синтеза речи
        self.current_tts_task = asyncio.create_task(self._tts_stream(text, context))

    async def _tts_stream(self, text, context):
        # Здесь вызывается tts (пример, замените на вашу реализацию)
        await self.tts.speak(text, context=context)

    async def start(self, session: AgentSession) -> None:
        # Приветствие будет произнесено в on_enter
        logger.info("Agent started successfully")

    async def shutdown(self):
        # Корректное завершение активной задачи при остановке агента
        if self.current_tts_task and not self.current_tts_task.done():
            self.current_tts_task.cancel()
            try:
                await self.current_tts_task
            except asyncio.CancelledError:
                pass

    @function_tool
    async def switch_slide(self, context: RunContext[UserData], index: int) -> str:
        """Switches the presentation to a specific slide index and returns its content."""
        current_time = time.time()
        if current_time - self.last_switch_time < self.switch_cooldown_seconds:
            return "Cooldown: Please wait a moment before switching slides again."

        self.last_switch_time = current_time
        userdata = context.userdata
        if not userdata.ctx or not userdata.ctx.room:
            return "Error: Couldn't access the room context."

        if not (0 <= index < len(userdata.slides)):
            logger.warning(f"Invalid slide index: {index}. Total slides: {len(userdata.slides)}")
            return f"Error: Slide index {index} is out of bounds."

        userdata.current_slide_index = index
        room = userdata.ctx.room
        participant = next(iter(room.remote_participants.values()), None)
        if not participant:
            logger.warning("No remote participant found to send RPC to.")
            return "Error: Couldn't find the participant to send the slide to."

        try:
            # The index is now the same for the agent and the client.
            payload = {"index": index}
            json_payload = json.dumps(payload)
            logger.info(f"Sending RPC 'client.switchslide' with payload: {json_payload} to {participant.identity}")
            await room.local_participant.perform_rpc(
                destination_identity=participant.identity,
                method="client.switchslide",
                payload=json_payload
            )
        except Exception as e:
            logger.error(f"Error sending slide switch RPC: {e}")
            return f"Error: {str(e)}"

        slide = userdata.slides[index]
        title = slide.get('title', 'No Title')
        # Обновляем формат сообщения, чтобы соответствовать prompt_llm.yaml
        return (
            f"Switched to slide with internal index {index} (user-facing number {index + 1}). "
            f"The title is '{slide['title']}'. You should now describe this slide to the user by its title. "
            f"Remember to NOT mention slide numbers to the user, as it sounds unnatural."
        )

    @function_tool
    async def next_slide(self, context: RunContext[UserData]) -> str:
        """Moves to the next slide in the presentation."""
        userdata = context.userdata
        new_index = userdata.current_slide_index + 1
        if new_index >= len(userdata.slides):
            return "This is the last slide. You can ask for more details or end the presentation."
        return await self.switch_slide(context, new_index)

    @function_tool
    async def previous_slide(self, context: RunContext[UserData]) -> str:
        """Moves to the previous slide in the presentation."""
        userdata = context.userdata
        new_index = userdata.current_slide_index - 1
        if new_index < 0:
            return "This is the first slide."
        return await self.switch_slide(context, new_index)
    


    async def on_enter(self):
        # Агент готов к работе, приветствие будет произнесено через LLM при первом взаимодействии
        logger.info("Agent entered the room and is ready to present")

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.shutdown()


async def entrypoint(ctx: JobContext):
    logger.info("Entrypoint started.")
    
    # Настройка CORS для веб-приложения
    if hasattr(ctx, 'app') and ctx.app is not None:
        logger.info("Setting up CORS for the web application")
        
        # Добавляем обработчик для маршрута /api/connection-details
        async def connection_details_handler(request):
            try:
                # Здесь должна быть ваша логика обработки запроса
                return web.json_response({
                    "status": "success", 
                    "message": "Connection details API"
                })
            except Exception as e:
                logger.error(f"Error in connection_details_handler: {e}")
                return web.json_response(
                    {"status": "error", "message": str(e)}, 
                    status=500
                )
        
        # Добавляем маршрут для API
        ctx.app.router.add_post('/api/connection-details', connection_details_handler)
        
        # Настраиваем CORS после добавления всех маршрутов
        cors = aiohttp_cors.setup(ctx.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*",
            )
        })
        
        # Применяем CORS ко всем маршрутам
        for route in list(ctx.app.router.routes()):
            try:
                cors.add(route)
                logger.info(f"Added CORS for route: {route}")
            except Exception as e:
                logger.error(f"Error adding CORS for route {route}: {e}")
    
    # Добавляем простой обработчик для корневого пути (для health check)
    if hasattr(ctx, 'app') and ctx.app is not None:
        async def health_handler(request):
            return web.json_response({"status": "healthy", "message": "Service is up and running"})
        
        ctx.app.router.add_get('/', health_handler)
    try:
        # Load presentation data
        # Try to find presentation.json in the same directory first (for Docker/Fly.io)
        local_path = Path(__file__).parent / 'presentation.json'
        # Fallback to the frontend path (for local development)
        frontend_path = Path(__file__).parent.parent / 'frontend' / 'data' / 'presentation.json'
        
        # Use local path if it exists, otherwise use frontend path
        presentation_path = local_path if local_path.exists() else frontend_path
        logger.info(f"Attempting to load presentation from: {presentation_path}")
        with open(presentation_path, 'r', encoding='utf-8') as file:
            presentation_data = json.load(file)
        
        slides = presentation_data.get('slides', [])
        if not slides:
            raise ValueError("Presentation data is empty or missing 'slides' key.")
        logger.info(f"Successfully loaded {len(slides)} slides.")

        await ctx.connect()
        logger.info("Connected to LiveKit room.")

        # Wait for a participant to join to get the language from metadata
        language = 'en' # Default language
        participant = None
        while participant is None:
            if len(ctx.room.remote_participants) > 0:
                participant = next(iter(ctx.room.remote_participants.values()))
            else:
                await asyncio.sleep(0.5)

        if participant and participant.metadata:
            try:
                metadata = json.loads(participant.metadata)
                language = metadata.get('language', language)
                logger.info(f"Language '{language}' selected from participant metadata.")
            except json.JSONDecodeError:
                logger.warning(f"Could not decode participant metadata: {participant.metadata}")

        # Prepare chat context for the LLM
        instructions = load_prompt('prompt_llm.yaml', 'llm_instructions')
        
        # Extract slide titles for a more concise prompt
        slide_titles = [f"{i}: {slide.get('title', f'Slide {i}')}" for i, slide in enumerate(slides)]
        slide_titles_str = "\n".join(f"- {title}" for title in slide_titles)

        # Combine instructions from YAML with dynamic data
        system_prompt = (
            f"{instructions}\n\n"
            f"Here are the slide titles:\n{slide_titles_str}\n\n"
            f"Your primary tools are `next_slide()`, `previous_slide()`, and `switch_slide(index)`. "
            f"Use them to navigate the presentation based on the user's requests. "
            f"When a slide is switched, you will receive its content to discuss.\n\n"
            f"IMPORTANT: Start and continue the presentation in {language}. "
            f"Only switch to another language if explicitly asked by the user."
        )

        chat_ctx = llm.ChatContext()
        chat_ctx.add_message(role="system", content=system_prompt)
        logger.info(f"Chat context prepared with instruction to use '{language}'.")

        # Create the agent
        agent = PresentationAgent(instructions=system_prompt, chat_ctx=chat_ctx)
        logger.info("Agent created.")

        # The agent starts at slide 0, which is the intro slide.
        userdata = UserData(ctx=ctx, slides=slides, current_slide_index=0)
        session = AgentSession[UserData](
            userdata=userdata,
            turn_detection="vad",
            min_endpointing_delay=1.2,
            max_endpointing_delay=6.0,
        )
        logger.info("Agent session created.")


        






        async def handle_update_settings(rpc_data):
            try:
                logger.info(f"Received update settings payload: {rpc_data}")
                payload_str = rpc_data.payload
                payload_data = json.loads(payload_str)
                audio_enabled = payload_data.get("audio")
                
                tracks = ctx.room.local_participant.track_publications
                for _, track in tracks.items():
                    if audio_enabled:
                        track.track.unmute()
                    else:
                        track.track.mute()

                for _, participant in ctx.room.remote_participants.items():
                    for _, p_track in participant.track_publications.items():
                        p_track.set_subscribed(audio_enabled)
                
                return None
            except Exception as e:
                logger.error(f"Error handling update settings: {e}")
                return f"error: {str(e)}"



        logger.info("Registering RPC method 'agent.updateSettings'")
        ctx.room.local_participant.register_rpc_method(
            "agent.updateSettings",
            handle_update_settings
        )
        


    except Exception as e:
        logger.exception(f"An error occurred in the entrypoint: {e}")
        raise

    async def cleanup():
        logger.info(f"Start clean up context...")
        try:
            ctx.shutdown("CLEAN")
            await ctx.room.disconnect()
            logger.info("Room closed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    # Добавляем обработчик закрытия
    ctx.add_shutdown_callback(cleanup)

    # Start the agent session
    logger.info("Starting agent session...")
    await session.start(
        room=ctx.room,
        room_output_options=RoomOutputOptions(
            audio_enabled=True,
        ),
        agent=agent
    )
    logger.info("Agent session started.")
    
    # Automatically switch to the intro slide (index 0) on the client
    participant = next(iter(ctx.room.remote_participants.values()), None)
    if participant:
        try:
            payload = {"index": 0}
            json_payload = json.dumps(payload)
            logger.info(f"Sending initial RPC 'client.switchslide' with payload: {json_payload} to {participant.identity}")
            await ctx.room.local_participant.perform_rpc(
                destination_identity=participant.identity,
                method="client.switchslide",
                payload=json_payload
            )
        except Exception as e:
            logger.error(f"Failed to send initial slide switch RPC: {e}")
    else:
        logger.warning("No remote participant found for initial slide switch.")

    # Generate a dynamic greeting for the user, instructing the LLM how to handle the intro state.
    greeting_instruction = (
        f"Greet the user in {language}. You are now on the introduction slide (index 0). "
        f"Briefly introduce the topic. Then, wait for the user to say 'next slide' or 'start'."
    )
    await session.generate_reply(instructions=greeting_instruction)
    logger.info(f"Generated initial greeting for user in {language}")

    # Keep the worker alive
    logger.info("Worker entering wait state...")
    await asyncio.Event().wait()
    logger.info("Worker wait state exited. This should not happen.")

def load_prompt(filename, key='instructions'):
    """Load a prompt from a YAML file by key."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    prompt_path = os.path.join(script_dir, filename)
    
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            prompt_data = yaml.safe_load(file)
            return prompt_data.get(key, '')
    except (FileNotFoundError, yaml.YAMLError) as e:
        print(f"Error loading prompt file {filename}: {e}")
        return ""




if __name__ == "__main__":
    # Настраиваем порт 8080 для Fly.io
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            port=8080  # Явно указываем порт 8080
        )
    )
