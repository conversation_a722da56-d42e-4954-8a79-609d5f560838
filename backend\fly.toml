# fly.toml file for agent-example

app = 'multiprez'
primary_region = 'iad' # Вы можете изменить регион позже, если необходимо

kill_timeout = "300s" # Максимальное время ожидания перед остановкой экземпляра

[build]
  dockerfile = "Dockerfile" # Dockerfile в текущей директории
  ignorefile = ".dockerignore" # Используемый .dockerignore

[env]
  PYTHONUNBUFFERED = '1'
  # PORT будет автоматически установлен Fly.io. Ваше приложение (слушающее на 8080) его подхватит.

[[vm]]
  memory = '4gb' # Объем памяти для VM
  cpu_kind = 'shared' # Тип CPU
  cpus = 2 # Количество CPU

[deploy]
  strategy = "bluegreen" # Стратегия развертывания для минимизации простоя

# Отключаем HTTP health check на уровне приложения, так как у нас нет эндпоинта /health
# [[checks]]
#   name = "health_check_http"
#   type = "http"
#   interval = "10s"
#   timeout = "2s"
#   grace_period = "30s"
#   port = 80
#   path = "/health"
#   method = "GET"
#   protocol = "http"

# Определение публичных сервисов
[[services]]
  protocol = "tcp"
  internal_port = 8080 # Внутренний порт, на котором слушает ваше приложение
  auto_stop_machines = false # Не останавливать машины при отсутствии трафика
  auto_start_machines = false # Запускать машины при появлении трафика
  min_machines_running = 1 # Всегда держим минимум 1 машину запущенной

  [[services.ports]]
    port = 80 # Внешний HTTP порт
    handlers = ["http"]

  [[services.ports]]
    port = 443 # Внешний HTTPS порт
    handlers = ["tls", "http"] # Fly.io обработает TLS

  # Добавляем простую TCP проверку для стратегии bluegreen
  [[services.tcp_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "30s"

  #проверка здоровья
  [[services.http_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "30s"
    path = "/"
    method = "get"
    port = 8080